<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> - CV</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
        }

        .container {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .profile-photo {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            border: 5px solid white;
            margin: 0 auto 20px;
            display: block;
            object-fit: cover;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #3498db;
            position: relative;
        }

        .section h2::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 50px;
            height: 3px;
            background: #e74c3c;
        }

        .education-item, .project-item {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .education-item h3, .project-item h3 {
            color: #2c3e50;
            font-size: 1.3em;
            margin-bottom: 8px;
        }

        .education-details {
            color: #666;
            margin-bottom: 10px;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .skill-category {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #e74c3c;
        }

        .skill-category h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .skill-list {
            list-style: none;
        }

        .skill-list li {
            padding: 5px 0;
            color: #555;
        }

        .skill-list li::before {
            content: '▸';
            color: #3498db;
            margin-right: 10px;
            font-weight: bold;
        }

        .objective {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 25px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            font-style: italic;
            line-height: 1.8;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .contact-info {
                flex-direction: column;
                gap: 15px;
            }

            .content {
                padding: 30px 20px;
            }

            .skills-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDE1MCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxNTAiIGhlaWdodD0iMTUwIiByeD0iNzUiIGZpbGw9IiNkZGQiLz4KPHRleHQgeD0iNzUiIHk9IjgwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTgiIGZpbGw9IiM2NjYiPlBob3RvPC90ZXh0Pgo8L3N2Zz4K" alt="Profile Photo" class="profile-photo">
            <h1>FADHL AMEEN HASAN ALI ALSHARIF</h1>
            <div class="subtitle">Artificial Intelligence Student</div>
            <div class="contact-info">
                <div class="contact-item">
                    <span>📧</span>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <span>📱</span>
                    <span>05395159441</span>
                </div>
                <div class="contact-item">
                    <span>🎓</span>
                    <span>Expected Graduation: May 2026</span>
                </div>
            </div>
        </div>

        <div class="content">
            <div class="section">
                <h2>OBJECTIVE</h2>
                <div class="objective">
                    An artificial intelligence student with a passion for learning and exploring beyond university courses. I have a keen interest in applying academic knowledge to diverse practical projects and am always eager to develop myself and acquire new skills in fields such as machine learning, data analysis, and natural language processing. I am proficient in programming languages Python, C++, C#, and SQL, and constantly seek to participate in larger challenges and projects that expand my experience and help me provide innovative solutions that serve the practical realm.
                </div>
            </div>

            <div class="section">
                <h2>EDUCATION</h2>
                <div class="education-item">
                    <h3>Bachelor's Degree in Artificial Intelligence</h3>
                    <div class="education-details">
                        <strong>Cyprus International University</strong><br>
                        Third-Year Student (2022 - Present)<br>
                        Expected Graduation: May 2026
                    </div>
                    <p>Currently pursuing comprehensive studies in artificial intelligence, including machine learning, data analysis, and natural language processing with strong foundation in programming and database management.</p>
                </div>
            </div>

            <div class="section">
                <h2>TECHNICAL SKILLS</h2>
                <div class="skills-grid">
                    <div class="skill-category">
                        <h3>Programming Languages</h3>
                        <ul class="skill-list">
                            <li>Python (Intermediate)</li>
                            <li>C++ (Proficient)</li>
                            <li>C# (Basic)</li>
                            <li>SQL (Database Management)</li>
                        </ul>
                    </div>
                    <div class="skill-category">
                        <h3>Languages</h3>
                        <ul class="skill-list">
                            <li>Arabic (Native)</li>
                            <li>English (Fluent)</li>
                        </ul>
                    </div>
                    <div class="skill-category">
                        <h3>Areas of Interest</h3>
                        <ul class="skill-list">
                            <li>Machine Learning</li>
                            <li>Data Analysis</li>
                            <li>Natural Language Processing</li>
                            <li>Database Design</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>ACADEMIC PROJECTS</h2>

                <div class="project-item">
                    <h3>Database II Project</h3>
                    <p><strong>Advanced Database System Design</strong></p>
                    <p>Designed and implemented an advanced database system containing more than six linked tables, featuring a comprehensive user system with permissions and different roles (administrator, employee, client, technician). This project demonstrated proficiency in database architecture, security implementation, and user management systems.</p>
                </div>

                <div class="project-item">
                    <h3>C++ Application Development</h3>
                    <p><strong>Programming Fundamentals Implementation</strong></p>
                    <p>Developed a comprehensive application using C++ to implement and demonstrate basic programming concepts. This project showcased understanding of object-oriented programming, data structures, and algorithm implementation.</p>
                </div>

                <div class="project-item">
                    <h3>C# Academic Project</h3>
                    <p><strong>Software Development</strong></p>
                    <p>Built a functional project using C# as part of academic coursework, demonstrating adaptability to different programming environments and frameworks.</p>
                </div>

                <div class="project-item">
                    <h3>Python & Data Structure Projects</h3>
                    <p><strong>Multiple Implementation Projects</strong></p>
                    <p>Implemented various small-scale projects using Python, focusing on data structures and SQL database integration. These projects enhanced practical programming skills and database connectivity.</p>
                </div>
            </div>

            <div class="section">
                <h2>PRACTICAL EXPERIENCE</h2>
                <div class="education-item">
                    <h3>University Coursework & Practical Training</h3>
                    <div class="education-details">
                        <strong>Cyprus International University</strong><br>
                        2022 - Present
                    </div>
                    <ul style="margin-top: 15px; padding-left: 20px;">
                        <li>Completed multiple practical exercises within university courses</li>
                        <li>Extensive programming assignments using C++, Python, and SQL databases</li>
                        <li>Applied theoretical knowledge through hands-on practical lessons</li>
                        <li>Participated in collaborative projects and problem-solving exercises</li>
                    </ul>
                </div>
            </div>

            <div class="section">
                <h2>PERSONAL QUALITIES</h2>
                <div class="skills-grid">
                    <div class="skill-category">
                        <h3>Key Strengths</h3>
                        <ul class="skill-list">
                            <li>Passionate about continuous learning</li>
                            <li>Strong problem-solving abilities</li>
                            <li>Eager to take on new challenges</li>
                            <li>Self-motivated and disciplined</li>
                        </ul>
                    </div>
                    <div class="skill-category">
                        <h3>Professional Goals</h3>
                        <ul class="skill-list">
                            <li>Gain practical industry experience</li>
                            <li>Apply academic knowledge to real projects</li>
                            <li>Develop innovative AI solutions</li>
                            <li>Contribute to meaningful projects</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>