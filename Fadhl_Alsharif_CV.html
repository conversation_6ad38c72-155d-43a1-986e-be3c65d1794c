<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> <PERSON><PERSON> - CV</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.5;
            color: #333;
            background-color: #f0f2f5;
        }

        .container {
            max-width: 210mm;
            margin: 20px auto;
            background: white;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            min-height: 297mm;
        }

        .header {
            background: #f8f9fa;
            padding: 40px;
            display: flex;
            align-items: center;
            gap: 40px;
        }

        .profile-photo {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid #2563eb;
            object-fit: cover;
            flex-shrink: 0;
        }

        .header-content {
            flex: 1;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 8px;
            font-weight: 700;
            color: #2563eb;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .header .subtitle {
            font-size: 1.1em;
            color: #6b7280;
            margin-bottom: 20px;
            text-transform: capitalize;
        }

        .contact-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 8px;
            font-size: 0.9em;
        }

        .contact-item {
            color: #4b5563;
        }

        .content {
            padding: 0 40px 40px 40px;
        }

        .section {
            margin-bottom: 35px;
        }

        .section h2 {
            color: #2563eb;
            font-size: 1.3em;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #2563eb;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .summary-text {
            color: #4b5563;
            line-height: 1.6;
            text-align: justify;
        }

        .education-item {
            margin-bottom: 20px;
        }

        .education-item h3 {
            color: #1f2937;
            font-size: 1.1em;
            margin-bottom: 5px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .education-details {
            color: #6b7280;
            margin-bottom: 8px;
        }

        .education-details strong {
            color: #1f2937;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .skill-list {
            list-style: none;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .skill-list li {
            color: #4b5563;
            padding: 2px 0;
            position: relative;
            padding-left: 15px;
        }

        .skill-list li::before {
            content: '•';
            color: #2563eb;
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        .project-item {
            margin-bottom: 20px;
        }

        .project-item h3 {
            color: #1f2937;
            font-size: 1em;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .project-item .project-type {
            color: #6b7280;
            font-style: italic;
            margin-bottom: 8px;
        }

        .project-item p {
            color: #4b5563;
            line-height: 1.5;
            text-align: justify;
        }

        .languages-section {
            display: flex;
            gap: 40px;
        }

        .language-item {
            color: #4b5563;
        }

        .language-item strong {
            color: #1f2937;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }

            .header {
                flex-direction: column;
                text-align: center;
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .content {
                padding: 20px;
            }

            .skills-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .skill-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiByeD0iNjAiIGZpbGw9IiNlNWU3ZWIiLz4KPHRleHQgeD0iNjAiIHk9IjY1IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0iU2Vnb2UgVUkiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM2YjcyODAiPlBob3RvPC90ZXh0Pgo8L3N2Zz4K" alt="Profile Photo" class="profile-photo">
            <div class="header-content">
                <h1>FADHL AMEEN HASAN ALI ALSHARIF</h1>
                <div class="subtitle">Artificial Intelligence Student</div>
                <div class="contact-info">
                    <div class="contact-item"><EMAIL></div>
                    <div class="contact-item">+90 ************</div>
                    <div class="contact-item">Expected Graduation: May 2026</div>
                </div>
            </div>
        </div>

        <div class="content">
            <div class="section">
                <h2>SUMMARY</h2>
                <div class="summary-text">
                    Motivated and detail-oriented Artificial Intelligence student with hands-on experience in machine learning, data analysis, and natural language processing. Proficient in programming languages like Python, C++, C# and SQL, and experienced in implementing AI algorithms using deep learning and NLP techniques. Passionate about solving real-world problems through innovative technology, and seeking opportunities to apply AI skills in a practical environment through summer training.
                </div>
            </div>

            <div class="section">
                <h2>EDUCATION</h2>
                <div class="education-item">
                    <h3>Cyprus International University</h3>
                    <div class="education-details">
                        • <strong>3rd year Bachelor of Artificial Intelligence engineering student</strong><br>
                        • <strong>Expected Graduation:</strong> [May, 2026]<br>
                        • <strong>Third-Year Student</strong> (2022 – Present)
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>SKILLS</h2>
                <div class="skills-grid">
                    <div>
                        <ul class="skill-list">
                            <li>NLP (Natural Language Processing)</li>
                            <li>Machine learning algorithm</li>
                            <li>Python (most commonly used)</li>
                            <li>C++</li>
                            <li>C#</li>
                            <li>Version Control: Git, GitHub</li>
                        </ul>
                    </div>
                    <div>
                        <ul class="skill-list">
                            <li>Pandas and NumPy for data manipulation</li>
                            <li>SQL for database queries</li>
                            <li>Excel</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>PROJECTS</h2>

                <div class="project-item">
                    <h3>Tweets Classification using LSTM (NLP Project)</h3>
                    <div class="project-type">Designed and managed a relational</div>
                    <p>Developed a deep learning model using (RNN) and (LSTM) to classify tweets as either natural disasters or unrelated content</p>
                </div>

                <div class="project-item">
                    <h3>Website with SQL Database Integration</h3>
                    <p>Developed a dynamic website and integrated it with a SQL database to manage and display data. Designed user interfaces for data input and retrieval, and implemented secure database queries to handle user requests</p>
                </div>

                <div class="project-item">
                    <h3>2D Car Game using C#</h3>
                    <p>Created a simple 2D car racing game as part of a visual programming course. Implemented game mechanics to increase or decrease car speed, detect pedestrians, and collect coins</p>
                </div>
            </div>

            <div class="section">
                <h2>COURSEES</h2>
                <div class="education-item">
                    <div class="education-details">
                        • Linear Algebra for Machine Learning ( Coursera )<br>
                        • Machine Learning for Engineers: Algorithms and Applications
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>LANGUAGES</h2>
                <div class="languages-section">
                    <div class="language-item">
                        <strong>Arabic</strong> (mother language)
                    </div>
                    <div class="language-item">
                        <strong>English</strong> (very good)
                    </div>
                    <div class="language-item">
                        <strong>Turkish</strong> (Good)
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>